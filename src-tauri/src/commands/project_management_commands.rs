use log::{error, info};
use tauri::command;
use serde::{Deserialize, Serialize};

use crate::models::project_management::{
    DrugGroup, Project, ProjectPagination, ProjectPersonnelRole, ProjectQuery, ProjectSponsor,
    ProjectWithDetails, ResearchDrug, Subsidy, SubsidyScheme,
};
use crate::repositories::project_management_repository::ProjectManagementRepository;

/// 项目导出查询参数
#[derive(Debug, Deserialize, Clone)]
pub struct ProjectExportQuery {
    /// 项目状态ID
    pub project_status_item_id: Option<i64>,
    /// 招募状态ID
    pub recruitment_status_item_id: Option<i64>,
    /// 疾病ID
    pub disease_item_id: Option<i64>,
    /// 研究分期ID
    pub project_stage_item_id: Option<i64>,
    /// 指定的项目ID列表（用于批量导出）
    pub project_ids: Option<Vec<String>>,
    /// 是否包含入排标准
    pub include_criteria: Option<bool>,
}

/// 项目管理命令

/// 获取项目列表
#[command]
pub fn get_projects_list(
    query: ProjectQuery,
    db_path: String,
) -> Result<ProjectPagination, String> {
    info!("获取项目列表，查询参数: {:?}", query);

    let repo = ProjectManagementRepository::new(db_path);

    match repo.get_projects(&query) {
        Ok(pagination) => Ok(pagination),
        Err(e) => {
            error!("获取项目列表失败: {}", e);
            Err(format!("获取项目列表失败: {}", e))
        }
    }
}

/// 获取项目详情
#[command]
pub fn get_project_details(
    project_id: String,
    db_path: String,
) -> Result<Option<ProjectWithDetails>, String> {
    info!("获取项目详情，项目ID: {}", project_id);

    let repo = ProjectManagementRepository::new(db_path);

    match repo.get_project_details(&project_id) {
        Ok(details) => Ok(details),
        Err(e) => {
            error!("获取项目详情失败: {}", e);
            Err(format!("获取项目详情失败: {}", e))
        }
    }
}

/// 创建项目
#[command]
pub fn pm_create_project(project: Project, db_path: String) -> Result<String, String> {
    info!("创建项目: {:?}", project);

    let repo = ProjectManagementRepository::new(db_path);

    match repo.create_project(&project) {
        Ok(project_id) => Ok(project_id),
        Err(e) => {
            error!("创建项目失败: {}", e);
            Err(format!("创建项目失败: {}", e))
        }
    }
}

/// 更新项目
#[command]
pub fn pm_update_project(project: Project, db_path: String) -> Result<(), String> {
    info!("更新项目: {:?}", project);

    let repo = ProjectManagementRepository::new(db_path);

    match repo.update_project(&project) {
        Ok(_) => Ok(()),
        Err(e) => {
            error!("更新项目失败: {}", e);
            Err(format!("更新项目失败: {}", e))
        }
    }
}

/// 保存项目完整信息
#[command]
pub fn save_project_with_details(
    project: Project,
    sponsors: Option<Vec<ProjectSponsor>>,
    research_drugs: Option<Vec<ResearchDrug>>,
    drug_groups: Option<Vec<DrugGroup>>,
    personnel: Option<Vec<ProjectPersonnelRole>>,
    subsidy_schemes: Vec<SubsidyScheme>,
    subsidies: Option<Vec<Subsidy>>,
    db_path: String,
) -> Result<String, String> {
    info!("[COMMAND ENTRY] save_project_with_details command invoked.");

    info!("保存项目完整信息: {:?}", project);

    if !subsidy_schemes.is_empty() {
        info!("收到补贴方案数量: {}", subsidy_schemes.len());
        for (i, scheme) in subsidy_schemes.iter().enumerate() {
            info!(
                "方案 {}: 名称={}, 金额={}, 包含补贴项={:?}",
                i + 1,
                scheme.scheme_name,
                scheme.total_amount,
                scheme.included_subsidies
            );
        }
    } else {
        info!("没有收到补贴方案数据 (传入的 Vec 为空)");
    }

    let repo = ProjectManagementRepository::new(db_path);

    match repo.save_project_with_details(
        &project,
        sponsors.as_ref(),
        research_drugs.as_ref(),
        drug_groups.as_ref(),
        personnel.as_ref(),
        &subsidy_schemes,
        subsidies.as_ref(),
    ) {
        Ok(project_id) => Ok(project_id),
        Err(e) => {
            error!("保存项目完整信息失败: {}", e);
            Err(format!("保存项目完整信息失败: {}", e))
        }
    }
}

/// 删除项目
#[command]
pub fn pm_delete_project(project_id: String, db_path: String) -> Result<(), String> {
    info!("删除项目，项目ID: {}", project_id);

    let repo = ProjectManagementRepository::new(db_path);

    match repo.delete_project(&project_id) {
        Ok(_) => Ok(()),
        Err(e) => {
            error!("删除项目失败: {}", e);
            Err(format!("删除项目失败: {}", e))
        }
    }
}

/// 获取项目导出数据
#[command]
pub fn get_projects_export_data(
    query: ProjectExportQuery,
    db_path: String,
) -> Result<Vec<ProjectWithDetails>, String> {
    info!("获取项目导出数据，查询参数: {:?}", query);

    let repo = ProjectManagementRepository::new(db_path);

    // 如果指定了项目ID列表，直接获取这些项目的详情
    if let Some(project_ids) = &query.project_ids {
        let mut projects_with_details = Vec::new();

        for project_id in project_ids {
            match repo.get_project_details(project_id) {
                Ok(Some(details)) => {
                    projects_with_details.push(details);
                }
                Ok(None) => {
                    info!("项目不存在，跳过: {}", project_id);
                    continue;
                }
                Err(e) => {
                    error!("获取项目详情失败: {}", e);
                    return Err(format!("获取项目详情失败: {}", e));
                }
            }
        }

        return Ok(projects_with_details);
    }

    // 构建查询参数
    let project_query = ProjectQuery {
        name: None,
        disease_item_id: query.disease_item_id,
        project_stage_item_id: query.project_stage_item_id,
        project_status_item_id: query.project_status_item_id,
        recruitment_status_item_id: query.recruitment_status_item_id,
        page: Some(1),
        page_size: Some(1000), // 设置较大的页面大小以获取所有符合条件的项目
        sort_by: Some("project_name".to_string()),
        sort_order: Some("asc".to_string()),
    };

    match repo.get_projects(&project_query) {
        Ok(pagination) => {
            // 获取每个项目的详细信息
            let mut projects_with_details = Vec::new();

            for project in pagination.items {
                if let Some(project_id) = &project.project.project_id {
                    match repo.get_project_details(project_id) {
                        Ok(Some(details)) => {
                            projects_with_details.push(details);
                        }
                        Ok(None) => {
                            // 项目不存在，跳过
                            continue;
                        }
                        Err(e) => {
                            error!("获取项目详情失败: {}", e);
                            return Err(format!("获取项目详情失败: {}", e));
                        }
                    }
                }
            }

            Ok(projects_with_details)
        }
        Err(e) => {
            error!("获取项目导出数据失败: {}", e);
            Err(format!("获取项目导出数据失败: {}", e))
        }
    }
}

/// 获取项目完整导出数据（包含入排标准）
#[command]
pub fn get_projects_complete_export_data(
    query: ProjectExportQuery,
    db_path: String,
) -> Result<Vec<serde_json::Value>, String> {
    info!("获取项目完整导出数据，查询参数: {:?}", query);

    // 首先获取项目基本数据
    let projects = get_projects_export_data(query.clone(), db_path.clone())?;

    let mut complete_projects = Vec::new();

    for project in projects {
        let mut project_json = serde_json::to_value(&project)
            .map_err(|e| format!("序列化项目数据失败: {}", e))?;

        // 如果需要包含入排标准
        if query.include_criteria.unwrap_or(true) {
            if let Some(project_id) = &project.project.project_id {
                // 尝试获取入排标准数据
                match get_project_criteria_data(project_id.clone(), db_path.clone()) {
                    Ok(criteria) => {
                        project_json["criteria"] = serde_json::to_value(criteria)
                            .unwrap_or(serde_json::Value::Array(vec![]));
                    }
                    Err(e) => {
                        info!("获取项目 {} 的入排标准失败: {}", project_id, e);
                        project_json["criteria"] = serde_json::Value::Array(vec![]);
                    }
                }
            }
        }

        // 添加导出元数据
        project_json["export_metadata"] = serde_json::json!({
            "export_date": chrono::Utc::now().to_rfc3339(),
            "export_version": "2.0.0",
            "includes_criteria": query.include_criteria.unwrap_or(true)
        });

        complete_projects.push(project_json);
    }

    Ok(complete_projects)
}

/// 获取项目入排标准数据的辅助函数
fn get_project_criteria_data(
    project_id: String,
    db_path: String,
) -> Result<Vec<serde_json::Value>, String> {
    use crate::repositories::rule_designer_repository::RuleDesignerRepository;
    use crate::models::rule_designer::ProjectCriterionQuery;

    let rule_repo = RuleDesignerRepository::new(db_path);

    // 获取入组标准
    let inclusion_query = ProjectCriterionQuery {
        project_id: project_id.clone(),
        criterion_type: Some("inclusion".to_string()),
    };

    // 获取排除标准
    let exclusion_query = ProjectCriterionQuery {
        project_id: project_id.clone(),
        criterion_type: Some("exclusion".to_string()),
    };

    let mut all_criteria = Vec::new();

    // 获取入组标准
    match rule_repo.get_project_criteria(&inclusion_query) {
        Ok(inclusion_criteria) => {
            for criterion in inclusion_criteria {
                all_criteria.push(serde_json::to_value(criterion)
                    .map_err(|e| format!("序列化入组标准失败: {}", e))?);
            }
        }
        Err(e) => {
            info!("获取入组标准失败: {}", e);
        }
    }

    // 获取排除标准
    match rule_repo.get_project_criteria(&exclusion_query) {
        Ok(exclusion_criteria) => {
            for criterion in exclusion_criteria {
                all_criteria.push(serde_json::to_value(criterion)
                    .map_err(|e| format!("序列化排除标准失败: {}", e))?);
            }
        }
        Err(e) => {
            info!("获取排除标准失败: {}", e);
        }
    }

    Ok(all_criteria)
}

/// 初始化项目管理相关表
#[command]
pub fn init_project_management_tables(db_path: String) -> Result<(), String> {
    info!("初始化项目管理相关表");

    let repo = ProjectManagementRepository::new(db_path);

    match repo.init_tables() {
        Ok(_) => Ok(()),
        Err(e) => {
            error!("初始化项目管理相关表失败: {}", e);
            Err(format!("初始化项目管理相关表失败: {}", e))
        }
    }
}

/// 检查数据库表和数据
#[command]
pub fn check_database_tables(db_path: String) -> Result<String, String> {
    info!("检查数据库表和数据");

    let repo = ProjectManagementRepository::new(db_path);

    match repo.check_database_tables() {
        Ok(result) => Ok(result),
        Err(e) => {
            error!("检查数据库表和数据失败: {}", e);
            Err(format!("检查数据库表和数据失败: {}", e))
        }
    }
}

/// 重置数据库表
#[command]
pub fn reset_database_tables(db_path: String) -> Result<String, String> {
    info!("重置数据库表");

    let repo = ProjectManagementRepository::new(db_path);

    match repo.reset_database_tables() {
        Ok(_) => Ok("数据库表已重置".to_string()),
        Err(e) => {
            error!("重置数据库表失败: {}", e);
            Err(format!("重置数据库表失败: {}", e))
        }
    }
}
