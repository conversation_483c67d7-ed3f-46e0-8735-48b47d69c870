<script lang="ts">
  import * as Dialog from "$lib/components/ui/dialog";
  import { Button } from "$lib/components/ui/button";
  import Select from "$lib/components/ui/select.svelte";
  import { FileDown, FileText, FileJson, Loader2, CheckCircle, AlertCircle, FolderOpen } from 'lucide-svelte';
  import { fileSystemService } from '$lib/services/fileSystemService';
  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';
  import { projectManagementService, type ProjectQuery } from '$lib/services/projectManagementService';
  import { onMount } from 'svelte';

  // 组件属性
  let { open = $bindable(false), projects = $bindable([]) } = $props();

  // 导出配置
  let exportFormat = $state('csv'); // 'csv' or 'json'
  let isExporting = $state(false);
  let exportResult = $state<{ success: boolean; message: string; filePath?: string } | null>(null);

  // 过滤器状态
  let selectedResearchStageId = $state<number | null>(null);
  let selectedProjectStatusId = $state<number | null>(null);
  let selectedDiseaseId = $state<number | null>(null);
  let isLoadingFilters = $state(false);
  let filterError = $state<string | null>(null);

  // 项目选择状态
  let selectedProjectIds = $state<string[]>([]);
  let selectAllProjects = $state(false);
  let showProjectSelection = $state(false);

  // 导出选项
  let exportOptions = $state({
    includeCriteria: true,
    includePersonnel: true,
    includeSubsidies: true,
    includeDrugs: true,
    includeSponsors: true
  });

  // 字典数据
  let researchStages = $state<{item_id: number, item_value: string}[]>([]);
  let projectStatuses = $state<{item_id: number, item_value: string}[]>([]);
  let diseases = $state<{item_id: number, item_value: string}[]>([]);

  // 预览数据
  let filteredProjectsCount = $state<number | null>(null);
  let isLoadingPreview = $state(false);

  // 加载字典数据
  async function loadDictionaryData() {
    try {
      isLoadingFilters = true;
      filterError = null;

      // 加载研究分期字典（注意：项目表中的project_stage_item_id实际指向研究分期，不是研究阶段）
      const researchStagesDict = await sqliteDictionaryService.getDictByName('研究分期');
      if (researchStagesDict && researchStagesDict.items) {
        researchStages = researchStagesDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
        console.log('研究分期字典加载成功:', researchStages);
      } else {
        console.warn('研究分期字典为空或未找到');
      }

      // 加载研究阶段字典（项目表中的project_status_item_id指向研究阶段）
      const projectStatusesDict = await sqliteDictionaryService.getDictByName('研究阶段');
      if (projectStatusesDict && projectStatusesDict.items) {
        projectStatuses = projectStatusesDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
        console.log('研究阶段字典加载成功:', projectStatuses);
      } else {
        console.warn('研究阶段字典为空或未找到');
      }

      // 加载疾病字典
      const diseasesDict = await sqliteDictionaryService.getDictByName('疾病');
      if (diseasesDict && diseasesDict.items) {
        diseases = diseasesDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
        console.log('疾病字典加载成功:', diseases);
      } else {
        console.warn('疾病字典为空或未找到');
      }
    } catch (error) {
      console.error('加载字典数据失败:', error);
      filterError = `加载筛选条件失败: ${error instanceof Error ? error.message : '未知错误'}`;
    } finally {
      isLoadingFilters = false;
    }
  }

  // 获取过滤后的项目数据
  async function getFilteredProjects() {
    try {
      // 构建查询参数
      const query: ProjectQuery = {
        disease_item_id: selectedDiseaseId || undefined,
        project_stage_item_id: selectedResearchStageId || undefined,
        project_status_item_id: selectedProjectStatusId || undefined,
        page: 1,
        page_size: 10000, // 获取所有匹配的项目
        sort_by: 'project_name',
        sort_order: 'asc'
      };

      // 获取过滤后的项目列表
      const result = await projectManagementService.getProjects(query);
      return result.items;
    } catch (error) {
      console.error('获取过滤项目失败:', error);
      throw error;
    }
  }

  // 预览过滤结果
  async function updatePreview() {
    if (!selectedResearchStageId && !selectedProjectStatusId && !selectedDiseaseId) {
      filteredProjectsCount = projects.length;
      return;
    }

    try {
      isLoadingPreview = true;
      const filteredProjects = await getFilteredProjects();
      filteredProjectsCount = filteredProjects.length;
    } catch (error) {
      console.error('预览过滤结果失败:', error);
      filteredProjectsCount = null;
    } finally {
      isLoadingPreview = false;
    }
  }

  // 监听过滤条件变化
  $effect(() => {
    updatePreview();
  });

  // 处理项目选择
  function toggleProjectSelection(projectId: string) {
    if (!projectId) return; // 防止空ID

    if (selectedProjectIds.includes(projectId)) {
      // 取消选择
      selectedProjectIds = selectedProjectIds.filter(id => id !== projectId);
    } else {
      // 添加选择
      selectedProjectIds = [...selectedProjectIds, projectId];
    }
    // 状态会通过 $effect 自动更新
  }

  function toggleSelectAll() {
    if (selectAllProjects) {
      // 全选：选择所有项目
      selectedProjectIds = projects.map(p => p.project.project_id).filter(Boolean) as string[];
    } else {
      // 取消全选：清空选择
      selectedProjectIds = [];
    }
  }

  function updateSelectAllState() {
    const totalProjects = projects.length;
    const selectedCount = selectedProjectIds.length;
    // 只有当选中数量等于总数且总数大于0时，才设置为全选状态
    selectAllProjects = selectedCount === totalProjects && totalProjects > 0;
  }

  // 监听项目选择变化，自动更新全选状态
  $effect(() => {
    updateSelectAllState();
  });

  // 获取所有项目中存在的角色类型
  function getAllRoleTypes(projectsData: any[]): string[] {
    const roleTypes = new Set<string>();

    projectsData.forEach(project => {
      if (project.personnel && Array.isArray(project.personnel)) {
        project.personnel.forEach((person: any) => {
          if (person.role?.item_value) {
            roleTypes.add(person.role.item_value);
          }
        });
      }
    });

    // 返回排序后的角色类型数组
    return Array.from(roleTypes).sort();
  }

  // 根据角色类型分组人员数据
  function groupPersonnelByRole(personnel: any[]): Record<string, string[]> {
    const roleGroups: Record<string, string[]> = {};

    if (!personnel || !Array.isArray(personnel)) {
      return roleGroups;
    }

    personnel.forEach(person => {
      const roleName = person.role?.item_value;
      const personName = person.personnel?.name;

      if (roleName && personName) {
        if (!roleGroups[roleName]) {
          roleGroups[roleName] = [];
        }
        roleGroups[roleName].push(personName);
      }
    });

    return roleGroups;
  }

  // 格式化启动日期，包含已启动天数
  function formatStartDateWithDays(startDateStr: string | null | undefined): string {
    if (!startDateStr || startDateStr.trim() === '') {
      return '未设置启动日期';
    }

    try {
      // 解析启动日期
      const startDate = new Date(startDateStr);

      // 检查日期是否有效
      if (isNaN(startDate.getTime())) {
        return '日期格式错误';
      }

      // 获取当前日期（只考虑日期部分，不考虑时间）
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // 设置启动日期的时间为0，只比较日期
      const startDateOnly = new Date(startDate);
      startDateOnly.setHours(0, 0, 0, 0);

      // 计算天数差
      const timeDiff = today.getTime() - startDateOnly.getTime();
      const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));

      // 格式化日期为中文格式
      const year = startDate.getFullYear();
      const month = startDate.getMonth() + 1;
      const day = startDate.getDate();
      const formattedDate = `${year}年${month.toString().padStart(2, '0')}月${day.toString().padStart(2, '0')}日`;

      // 根据天数差返回不同格式
      if (daysDiff < 0) {
        // 未来日期
        return `${formattedDate}（未启动）`;
      } else if (daysDiff === 0) {
        // 今天启动
        return `${formattedDate}（今日启动）`;
      } else {
        // 已启动
        return `${formattedDate}（已启动${daysDiff}天）`;
      }
    } catch (error) {
      console.error('日期格式化错误:', error);
      return '日期处理错误';
    }
  }

  // 计算两个日期之间的天数差（用于性能优化的简化版本）
  function calculateDaysDifference(startDateStr: string | null | undefined): number {
    if (!startDateStr) return -1;

    try {
      const startDate = new Date(startDateStr);
      const today = new Date();

      if (isNaN(startDate.getTime())) return -1;

      // 只比较日期部分
      startDate.setHours(0, 0, 0, 0);
      today.setHours(0, 0, 0, 0);

      return Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    } catch {
      return -1;
    }
  }

  // 定义需要导出的入组标准字段
  const INCLUSION_CRITERIA_FIELDS = [
    "年龄",
    "慢阻肺、哮喘吸入药物维持治疗",
    "慢阻肺病或哮喘急性发作病史",
    "支气管舒张剂前（Pre-BD）FEV1",
    "确诊史",
    "血嗜酸粒细胞计数"
  ];

  // 提取项目的入组标准字段值
  function extractInclusionCriteriaFields(criteria: any[], projectName?: string): Record<string, string> {
    const result: Record<string, string> = {};

    // 初始化所有字段为空字符串
    INCLUSION_CRITERIA_FIELDS.forEach(field => {
      result[field] = '';
    });

    if (!criteria || !Array.isArray(criteria)) {
      console.log(`[调试] ${projectName || '未知项目'}: criteria 数据为空或不是数组`, criteria);
      return result;
    }

    console.log(`[调试] ${projectName || '未知项目'}: 原始 criteria 数据:`, criteria);

    // 筛选出入组标准 - 处理两种可能的数据结构
    const inclusionCriteria = criteria.filter(item => {
      // 检查是否是包装结构 {criterion: Object, rule_definition: Object}
      if (item.criterion && item.criterion.criterion_type) {
        return item.criterion.criterion_type === 'inclusion';
      }
      // 检查是否是直接的 criterion 对象
      if (item.criterion_type) {
        return item.criterion_type === 'inclusion';
      }
      return false;
    });

    console.log(`[调试] ${projectName || '未知项目'}: 筛选出的入组标准数量: ${inclusionCriteria.length}`, inclusionCriteria);

    // 遍历入组标准，匹配指定字段
    inclusionCriteria.forEach((item, index) => {
      try {
        // 处理两种可能的数据结构
        let criterion, ruleDefinition;
        if (item.criterion && item.rule_definition) {
          // 包装结构 {criterion: Object, rule_definition: Object}
          criterion = item.criterion;
          ruleDefinition = item.rule_definition;
        } else {
          // 直接的 criterion 对象
          criterion = item;
          ruleDefinition = item.rule_definition;
        }

        // 尝试从规则名称匹配
        const ruleName = ruleDefinition?.rule_name || criterion.rule_name || '';
        const ruleDescription = ruleDefinition?.rule_description || '';

        console.log(`[调试] ${projectName || '未知项目'}: 处理第${index + 1}个入组标准:`, {
          ruleName,
          ruleDescription,
          criterion_type: criterion.criterion_type,
          parameter_values: criterion.parameter_values,
          dataStructure: item.criterion ? 'wrapped' : 'direct'
        });

        // 尝试解析参数值
        let parameterValues: any = {};
        if (criterion.parameter_values) {
          if (typeof criterion.parameter_values === 'string') {
            try {
              parameterValues = JSON.parse(criterion.parameter_values);
            } catch (parseError) {
              console.warn(`[调试] ${projectName || '未知项目'}: 参数值JSON解析失败:`, parseError);
              parameterValues = { raw_value: criterion.parameter_values };
            }
          } else {
            parameterValues = criterion.parameter_values;
          }
        }

        console.log(`[调试] ${projectName || '未知项目'}: 解析后的参数值:`, parameterValues);

        // 检查每个目标字段
        INCLUSION_CRITERIA_FIELDS.forEach(targetField => {
          let matched = false;
          let matchMethod = '';
          let extractedValue = '';

          // 方法1: 直接匹配规则名称
          if (ruleName && ruleName.includes(targetField)) {
            extractedValue = extractCriterionValue(criterion, parameterValues, ruleDefinition, targetField);
            if (extractedValue && !result[targetField]) {
              result[targetField] = extractedValue;
              matched = true;
              matchMethod = '规则名称匹配';
            }
          }

          // 方法2: 模糊匹配规则名称（处理可能的变体）
          if (!matched && ruleName) {
            const normalizedRuleName = ruleName.toLowerCase().replace(/\s+/g, '');
            const normalizedTargetField = targetField.toLowerCase().replace(/\s+/g, '');

            // 检查关键词匹配
            if (targetField === '年龄' && (normalizedRuleName.includes('年龄') || normalizedRuleName.includes('age'))) {
              extractedValue = extractCriterionValue(criterion, parameterValues, ruleDefinition, targetField);
              if (extractedValue && !result[targetField]) {
                result[targetField] = extractedValue;
                matched = true;
                matchMethod = '年龄关键词匹配';
              }
            } else if (targetField.includes('慢阻肺') && (normalizedRuleName.includes('慢阻肺') || normalizedRuleName.includes('copd'))) {
              extractedValue = extractCriterionValue(criterion, parameterValues, ruleDefinition, targetField);
              if (extractedValue && !result[targetField]) {
                result[targetField] = extractedValue;
                matched = true;
                matchMethod = '慢阻肺关键词匹配';
              }
            } else if (targetField.includes('FEV1') && (normalizedRuleName.includes('fev1') || normalizedRuleName.includes('肺功能'))) {
              extractedValue = extractCriterionValue(criterion, parameterValues, ruleDefinition, targetField);
              if (extractedValue && !result[targetField]) {
                result[targetField] = extractedValue;
                matched = true;
                matchMethod = 'FEV1关键词匹配';
              }
            } else if (targetField.includes('确诊史') && (normalizedRuleName.includes('确诊') || normalizedRuleName.includes('诊断'))) {
              extractedValue = extractCriterionValue(criterion, parameterValues, ruleDefinition, targetField);
              if (extractedValue && !result[targetField]) {
                result[targetField] = extractedValue;
                matched = true;
                matchMethod = '确诊史关键词匹配';
              }
            } else if (targetField.includes('血嗜酸粒细胞') && (normalizedRuleName.includes('嗜酸') || normalizedRuleName.includes('eosinophil'))) {
              extractedValue = extractCriterionValue(criterion, parameterValues, ruleDefinition, targetField);
              if (extractedValue && !result[targetField]) {
                result[targetField] = extractedValue;
                matched = true;
                matchMethod = '血嗜酸粒细胞关键词匹配';
              }
            }
          }

          // 方法3: 在参数值中查找字段名
          if (!matched && parameterValues && typeof parameterValues === 'object') {
            Object.keys(parameterValues).forEach(key => {
              if (!matched && (key.includes(targetField) || String(parameterValues[key]).includes(targetField))) {
                extractedValue = extractCriterionValue(criterion, parameterValues, ruleDefinition, targetField);
                if (extractedValue && !result[targetField]) {
                  result[targetField] = extractedValue;
                  matched = true;
                  matchMethod = '参数值匹配';
                }
              }
            });
          }

          // 方法4: 检查规则描述
          if (!matched && ruleDescription && ruleDescription.includes(targetField)) {
            extractedValue = extractCriterionValue(criterion, parameterValues, ruleDefinition, targetField);
            if (extractedValue && !result[targetField]) {
              result[targetField] = extractedValue;
              matched = true;
              matchMethod = '规则描述匹配';
            }
          }

          if (matched) {
            console.log(`[调试] ${projectName || '未知项目'}: 字段 "${targetField}" 匹配成功`, {
              matchMethod,
              extractedValue,
              ruleName,
              parameterValues
            });
          }
        });
      } catch (error) {
        console.warn(`[调试] ${projectName || '未知项目'}: 解析入组标准时出错:`, error);
      }
    });

    console.log(`[调试] ${projectName || '未知项目'}: 最终提取结果:`, result);

    // 统计匹配成功的字段数量
    const matchedFields = Object.keys(result).filter(key => result[key] !== '');
    console.log(`[调试] ${projectName || '未知项目'}: 成功匹配 ${matchedFields.length}/${INCLUSION_CRITERIA_FIELDS.length} 个字段:`, matchedFields);

    return result;
  }

  // 字段特定的格式化函数
  function formatAgeField(parameterValues: any): string {
    if (parameterValues.Max_age !== undefined && parameterValues.Min_age !== undefined) {
      return `最大年龄: ${parameterValues.Max_age} 岁, 最小年龄: ${parameterValues.Min_age} 岁`;
    } else if (parameterValues.Max_age !== undefined) {
      return `最大年龄: ${parameterValues.Max_age} 岁`;
    } else if (parameterValues.Min_age !== undefined) {
      return `最小年龄: ${parameterValues.Min_age} 岁`;
    }
    return '';
  }

  function formatMaintenanceTreatmentField(parameterValues: any): string {
    const parts = [];

    // 检查药物类型的多种可能字段名
    const drugTypeFields = ['drug_type', 'medication_type', 'treatment_type', 'therapy_type'];
    for (const field of drugTypeFields) {
      if (parameterValues[field]) {
        parts.push(`维持治疗药物类型: ${parameterValues[field]}`);
        break;
      }
    }

    // 检查维持时间的多种可能字段名
    const durationFields = ['maintenance_duration', 'duration', 'treatment_duration', 'months'];
    for (const field of durationFields) {
      if (parameterValues[field] !== undefined) {
        const duration = parameterValues[field];
        const unit = parameterValues.duration_unit || parameterValues.unit || '月';
        parts.push(`至少维持使用: ${duration} ${unit}`);
        break;
      }
    }

    // 检查稳定期的多种可能字段名
    const stableFields = ['stable_duration', 'stable_period', 'stable_months'];
    for (const field of stableFields) {
      if (parameterValues[field] !== undefined) {
        const stableDuration = parameterValues[field];
        const unit = parameterValues.stable_unit || parameterValues.unit || '月';
        parts.push(`剂量稳定至少: ${stableDuration} ${unit}`);
        break;
      }
    }

    // 如果没有找到特定字段，尝试从通用字段中提取信息
    if (parts.length === 0) {
      const allKeys = Object.keys(parameterValues);
      const relevantKeys = allKeys.filter(key =>
        key.toLowerCase().includes('month') ||
        key.toLowerCase().includes('duration') ||
        key.toLowerCase().includes('treatment') ||
        key.toLowerCase().includes('medication')
      );

      if (relevantKeys.length > 0) {
        const values = relevantKeys.map(key => `${key}: ${parameterValues[key]}`);
        return values.join(', ');
      }
    }

    return parts.join(', ');
  }

  function formatDiagnosisHistoryField(parameterValues: any): string {
    // 检查各种可能的时间字段
    const timeFields = [
      { field: 'duration', defaultUnit: '个月' },
      { field: 'months', defaultUnit: '个月' },
      { field: 'years', defaultUnit: '年' },
      { field: 'time', defaultUnit: '个月' },
      { field: 'period', defaultUnit: '个月' },
      { field: 'diagnosis_duration', defaultUnit: '个月' }
    ];

    for (const { field, defaultUnit } of timeFields) {
      if (parameterValues[field] !== undefined) {
        const value = parameterValues[field];
        const unit = parameterValues.unit || parameterValues.time_unit || defaultUnit;
        return `${value}${unit}`;
      }
    }

    // 如果没有找到标准字段，检查所有数值字段
    const numericKeys = Object.keys(parameterValues).filter(key => {
      const value = parameterValues[key];
      return typeof value === 'number' || (!isNaN(Number(value)) && value !== '');
    });

    if (numericKeys.length > 0) {
      const firstKey = numericKeys[0];
      const value = parameterValues[firstKey];
      const unit = parameterValues.unit || '个月';
      return `${value}${unit}`;
    }

    return '';
  }

  function formatExacerbationHistoryField(parameterValues: any): string {
    const parts = [];

    if (parameterValues.time_period || parameterValues.period) {
      const period = parameterValues.time_period || parameterValues.period;
      const unit = parameterValues.period_unit || '个月';
      parts.push(`${period}${unit}内`);
    }

    if (parameterValues.frequency || parameterValues.count) {
      const count = parameterValues.frequency || parameterValues.count;
      parts.push(`${count}次`);
    }

    if (parameterValues.severity || parameterValues.grade) {
      const severity = parameterValues.severity || parameterValues.grade;
      parts.push(`${severity}发作`);
    }

    return parts.join('');
  }

  function formatEosinophilField(parameterValues: any): string {
    if (parameterValues.min_count !== undefined) {
      const unit = parameterValues.unit || 'cells/μL';
      return `≥${parameterValues.min_count} ${unit}`;
    } else if (parameterValues.threshold !== undefined) {
      const unit = parameterValues.unit || 'cells/μL';
      return `≥${parameterValues.threshold} ${unit}`;
    } else if (parameterValues.count !== undefined) {
      const unit = parameterValues.unit || 'cells/μL';
      return `${parameterValues.count} ${unit}`;
    }
    return '';
  }

  function formatFEV1Field(parameterValues: any): string {
    const parts = [];

    if (parameterValues.min_percentage !== undefined && parameterValues.max_percentage !== undefined) {
      parts.push(`FEV1 ${parameterValues.min_percentage}-${parameterValues.max_percentage}%`);
    } else if (parameterValues.min_percentage !== undefined) {
      parts.push(`FEV1 ≥${parameterValues.min_percentage}%`);
    } else if (parameterValues.max_percentage !== undefined) {
      parts.push(`FEV1 ≤${parameterValues.max_percentage}%`);
    }

    if (parameterValues.measurement_condition) {
      parts.push(`(${parameterValues.measurement_condition})`);
    }

    return parts.join(' ');
  }

  // 提取标准值的辅助函数
  function extractCriterionValue(criterion: any, parameterValues: any, ruleDefinition?: any, targetField?: string): string {
    try {
      console.log('[调试] 提取标准值:', { criterion, parameterValues, targetField });

      // 优先使用字段特定的格式化逻辑
      if (parameterValues && typeof parameterValues === 'object' && targetField) {
        let formattedValue = '';

        switch (targetField) {
          case '年龄':
            formattedValue = formatAgeField(parameterValues);
            break;
          case '慢阻肺、哮喘吸入药物维持治疗':
            formattedValue = formatMaintenanceTreatmentField(parameterValues);
            break;
          case '确诊史':
            formattedValue = formatDiagnosisHistoryField(parameterValues);
            break;
          case '慢阻肺病或哮喘急性发作病史':
            formattedValue = formatExacerbationHistoryField(parameterValues);
            break;
          case '血嗜酸粒细胞计数':
            formattedValue = formatEosinophilField(parameterValues);
            break;
          case '支气管舒张剂前（Pre-BD）FEV1':
            formattedValue = formatFEV1Field(parameterValues);
            break;
        }

        if (formattedValue) {
          console.log(`[调试] 字段特定格式化结果 "${targetField}":`, formattedValue);
          return formattedValue;
        }
      }

      // 如果字段特定格式化失败，使用通用逻辑
      if (parameterValues && typeof parameterValues === 'object') {
        // 常见的参数字段
        const valueFields = ['value', 'min_value', 'max_value', 'range', 'condition', 'requirement', 'text', 'description'];

        for (const field of valueFields) {
          if (parameterValues[field] !== undefined && parameterValues[field] !== null && parameterValues[field] !== '') {
            const value = parameterValues[field];
            console.log(`[调试] 找到参数字段 "${field}":`, value);
            if (typeof value === 'object') {
              return JSON.stringify(value);
            }
            return String(value);
          }
        }

        // 检查所有非空字段
        const nonEmptyFields = Object.keys(parameterValues).filter(key => {
          const value = parameterValues[key];
          return value !== undefined && value !== null && value !== '';
        });

        if (nonEmptyFields.length > 0) {
          console.log('[调试] 找到非空字段:', nonEmptyFields);
          // 返回第一个非空字段的值
          const firstField = nonEmptyFields[0];
          const value = parameterValues[firstField];
          if (typeof value === 'object') {
            return JSON.stringify(value);
          }
          return String(value);
        }

        // 如果没有找到标准字段，返回整个参数对象的字符串表示
        const paramStr = JSON.stringify(parameterValues);
        if (paramStr !== '{}' && paramStr !== 'null') {
          console.log('[调试] 返回完整参数对象:', paramStr);
          return paramStr;
        }
      }

      // 备选方案：使用规则名称
      const ruleName = ruleDefinition?.rule_name || criterion.rule_definition?.rule_name || criterion.rule_name || '';
      if (ruleName) {
        console.log('[调试] 使用规则名称作为值:', ruleName);
        return ruleName;
      }

      // 最后备选：使用规则描述
      const ruleDescription = ruleDefinition?.rule_description || criterion.rule_definition?.rule_description || '';
      if (ruleDescription) {
        console.log('[调试] 使用规则描述作为值:', ruleDescription);
        return ruleDescription;
      }

      console.log('[调试] 没有找到有效值，返回默认值');
      return '已设置';
    } catch (error) {
      console.warn('[调试] 提取标准值时出错:', error);
      return '数据解析错误';
    }
  }

  // 调试项目数据结构
  async function debugProjectData() {
    try {
      console.log('[调试] 开始检查项目数据结构...');

      // 获取一个测试项目的完整数据
      const testProjectIds = projects.slice(0, 1).map(p => p.project.project_id).filter(Boolean) as string[];
      if (testProjectIds.length === 0) {
        console.log('[调试] 没有可用的项目进行测试');
        return;
      }

      const testData = await projectManagementService.getProjectsCompleteExportData({
        project_ids: testProjectIds,
        include_criteria: true
      });

      console.log('[调试] 获取到的完整项目数据:', testData);

      if (testData && testData.length > 0) {
        const firstProject = testData[0];
        console.log('[调试] 第一个项目的数据结构:', firstProject);
        console.log('[调试] criteria 字段:', firstProject.criteria);

        if (firstProject.criteria && Array.isArray(firstProject.criteria)) {
          console.log('[调试] criteria 数组长度:', firstProject.criteria.length);
          firstProject.criteria.forEach((criterion: any, index: number) => {
            console.log(`[调试] criteria[${index}]:`, criterion);
          });
        }
      }
    } catch (error) {
      console.error('[调试] 检查项目数据失败:', error);
    }
  }

  // 执行导出
  async function executeExport() {
    isExporting = true;
    exportResult = null;

    try {
      let projectsToExport;

      // 如果选择了特定项目，则导出选中的项目
      if (showProjectSelection && selectedProjectIds.length > 0) {
        // 使用完整导出数据获取选中项目的详细信息
        projectsToExport = await projectManagementService.getProjectsCompleteExportData({
          project_ids: selectedProjectIds,
          include_criteria: exportOptions.includeCriteria
        });
      } else if (selectedResearchStageId || selectedProjectStatusId || selectedDiseaseId) {
        // 如果设置了过滤条件，则获取过滤后的数据
        projectsToExport = await projectManagementService.getProjectsCompleteExportData({
          project_stage_item_id: selectedResearchStageId || undefined,
          project_status_item_id: selectedProjectStatusId || undefined,
          disease_item_id: selectedDiseaseId || undefined,
          include_criteria: exportOptions.includeCriteria
        });
      } else {
        // 否则导出所有项目的完整数据
        const projectIds = projects.map(p => p.project.project_id).filter(Boolean) as string[];
        projectsToExport = await projectManagementService.getProjectsCompleteExportData({
          project_ids: projectIds,
          include_criteria: exportOptions.includeCriteria
        });
      }

      if (!projectsToExport || projectsToExport.length === 0) {
        exportResult = { success: false, message: '没有符合条件的项目数据可导出' };
        return;
      }

      // 准备导出数据
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const filterSuffix = getFilterSuffix();
      const selectionSuffix = showProjectSelection && selectedProjectIds.length > 0
        ? `_选中${selectedProjectIds.length}项` : '';
      const fileName = `项目完整数据${filterSuffix}${selectionSuffix}_${timestamp}.${exportFormat}`;

      let content: string;
      if (exportFormat === 'csv') {
        content = generateEnhancedCSVContent(projectsToExport);
      } else {
        content = generateEnhancedJSONContent(projectsToExport);
      }

      // 调用后端导出功能
      const result = await fileSystemService.exportProjectsToFolder(content, fileName, exportFormat);

      if (result.success && result.data) {
        const filePath = result.data.file_path;
        exportResult = {
          success: true,
          message: `文件已成功导出到: ${filePath}`,
          filePath
        };
      } else {
        exportResult = {
          success: false,
          message: result.error || '导出失败'
        };
      }
    } catch (error) {
      console.error('导出失败:', error);
      exportResult = {
        success: false,
        message: `导出失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    } finally {
      isExporting = false;
    }
  }

  // 生成过滤条件后缀
  function getFilterSuffix(): string {
    const filters = [];
    if (selectedResearchStageId) {
      const stage = researchStages.find(s => s.item_id === selectedResearchStageId);
      if (stage) filters.push(stage.item_value);
    }
    if (selectedProjectStatusId) {
      const status = projectStatuses.find(s => s.item_id === selectedProjectStatusId);
      if (status) filters.push(status.item_value);
    }
    if (selectedDiseaseId) {
      const disease = diseases.find(d => d.item_id === selectedDiseaseId);
      if (disease) filters.push(disease.item_value);
    }
    return filters.length > 0 ? `_${filters.join('_')}` : '';
  }

  // 生成CSV内容
  function generateCSVContent(projectsData: any[]): string {
    // 获取所有角色类型
    const allRoleTypes = getAllRoleTypes(projectsData);

    // 准备CSV标题行
    const headers = [
      '项目全称',
      '项目简称',
      '疾病',
      '研究分期',
      '项目状态',
      '招募状态',
      '启动日期',
      '药物作用机制',
      '药物介绍',
      ...allRoleTypes, // 动态添加角色列
      ...INCLUSION_CRITERIA_FIELDS // 添加入组标准字段列
    ];

    // 准备CSV数据行
    const rows = projectsData.map(project => {
      // 基本项目信息
      const basicInfo = [
        project.project.project_name || '',
        project.project.project_short_name || '',
        project.disease?.item_value || '',
        project.project_stage?.item_value || '',
        project.project_status?.item_value || '',
        project.recruitment_status?.item_value || '',
        formatStartDateWithDays(project.project.project_start_date), // 使用增强的日期格式
        project.project.drug_mechanism || '',
        project.project.drug_introduction || ''
      ];

      // 按角色分组人员数据
      const roleGroups = groupPersonnelByRole(project.personnel || []);

      // 为每个角色类型生成对应的列数据
      const roleColumns = allRoleTypes.map(roleType => {
        const personnel = roleGroups[roleType] || [];
        return personnel.join(', '); // 用逗号分隔多个人员
      });

      // 提取入组标准字段值
      const projectName = project.project?.project_name || project.project?.project_short_name || '未知项目';
      const inclusionCriteriaFields = extractInclusionCriteriaFields(project.criteria || [], projectName);
      const inclusionColumns = INCLUSION_CRITERIA_FIELDS.map(field =>
        inclusionCriteriaFields[field] || ''
      );

      return [...basicInfo, ...roleColumns, ...inclusionColumns];
    });

    // 组合CSV内容
    return [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${(cell || '').toString().replace(/"/g, '""')}"`).join(','))
    ].join('\n');
  }

  // 生成JSON内容
  function generateJSONContent(projectsData: any[]): string {
    // 获取所有角色类型
    const allRoleTypes = getAllRoleTypes(projectsData);

    // 准备JSON数据
    const jsonData = projectsData.map(project => {
      // 基本项目信息
      const basicInfo = {
        project_name: project.project.project_name,
        project_short_name: project.project.project_short_name,
        disease: project.disease?.item_value,
        project_stage: project.project_stage?.item_value,
        project_status: project.project_status?.item_value,
        recruitment_status: project.recruitment_status?.item_value,
        project_start_date: formatStartDateWithDays(project.project.project_start_date), // 使用增强的日期格式
        project_start_date_raw: project.project.project_start_date, // 保留原始日期用于程序处理
        drug_mechanism: project.project.drug_mechanism,
        drug_introduction: project.project.drug_introduction
      };

      // 按角色分组人员数据
      const roleGroups = groupPersonnelByRole(project.personnel || []);
      const personnelByRole: Record<string, string[]> = {};

      // 为每个角色类型生成对应的数据
      allRoleTypes.forEach(roleType => {
        personnelByRole[roleType] = roleGroups[roleType] || [];
      });

      // 提取入组标准字段
      const projectName = project.project?.project_name || project.project?.project_short_name || '未知项目';
      const inclusionCriteriaFields = extractInclusionCriteriaFields(project.criteria || [], projectName);

      return {
        ...basicInfo,
        personnel_by_role: personnelByRole,
        inclusion_criteria: inclusionCriteriaFields
      };
    });

    // 添加元数据
    const exportData = {
      metadata: {
        export_version: '2.0.0',
        total_projects: jsonData.length,
        role_types: allRoleTypes,
        filters: {
          research_stage: selectedResearchStageId ? researchStages.find(s => s.item_id === selectedResearchStageId)?.item_value : null,
          project_status: selectedProjectStatusId ? projectStatuses.find(s => s.item_id === selectedProjectStatusId)?.item_value : null,
          disease: selectedDiseaseId ? diseases.find(d => d.item_id === selectedDiseaseId)?.item_value : null
        }
      },
      projects: jsonData
    };

    return JSON.stringify(exportData, null, 2);
  }

  // 生成增强的CSV内容（包含所有关联数据）
  function generateEnhancedCSVContent(projectsData: any[]): string {
    // 获取所有角色类型
    const allRoleTypes = getAllRoleTypes(projectsData);

    // 准备CSV标题行
    const headers = [
      '项目全称', '项目简称', '疾病', '研究分期', '项目状态', '招募状态',
      '启动日期', '药物作用机制', '药物介绍', '申办方', '研究药物', '药物分组',
      ...allRoleTypes, // 动态添加角色列，替换原来的"人员角色"
      ...INCLUSION_CRITERIA_FIELDS, // 添加入组标准字段列
      '补贴方案', '入排标准数量'
    ];

    // 准备CSV数据行
    const rows = projectsData.map(project => {
      // 处理申办方
      const sponsors = project.sponsors?.map((s: any) => s.sponsor?.item_value || '').join('; ') || '';

      // 处理研究药物
      const drugs = project.research_drugs?.map((d: any) => d.research_drug || '').join('; ') || '';

      // 处理药物分组
      const drugGroups = project.drug_groups?.map((g: any) => `${g.drug_name}(${g.share}%)`).join('; ') || '';

      // 按角色分组人员数据
      const roleGroups = groupPersonnelByRole(project.personnel || []);

      // 处理补贴方案
      const subsidySchemes = project.subsidy_schemes?.map((s: any) =>
        `${s.scheme_name}(${s.total_amount}元)`
      ).join('; ') || '';

      // 处理入排标准
      const criteriaCount = project.criteria?.length || 0;

      // 基本项目信息
      const basicInfo = [
        project.project?.project_name || '',
        project.project?.project_short_name || '',
        project.disease?.item_value || '',
        project.project_stage?.item_value || '',
        project.project_status?.item_value || '',
        project.recruitment_status?.item_value || '',
        formatStartDateWithDays(project.project?.project_start_date), // 使用增强的日期格式
        project.project?.drug_mechanism || '',
        project.project?.drug_introduction || '',
        sponsors,
        drugs,
        drugGroups
      ];

      // 为每个角色类型生成对应的列数据
      const roleColumns = allRoleTypes.map(roleType => {
        const personnel = roleGroups[roleType] || [];
        return personnel.join(', '); // 用逗号分隔多个人员
      });

      // 提取入组标准字段值
      const projectName = project.project?.project_name || project.project?.project_short_name || '未知项目';
      const inclusionCriteriaFields = extractInclusionCriteriaFields(project.criteria || [], projectName);
      const inclusionColumns = INCLUSION_CRITERIA_FIELDS.map(field =>
        inclusionCriteriaFields[field] || ''
      );

      return [
        ...basicInfo,
        ...roleColumns,
        ...inclusionColumns,
        subsidySchemes,
        criteriaCount.toString()
      ];
    });

    // 组合CSV内容
    return [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${(cell || '').toString().replace(/"/g, '""')}"`).join(','))
    ].join('\n');
  }

  // 生成增强的JSON内容（包含所有关联数据）
  function generateEnhancedJSONContent(projectsData: any[]): string {
    // 清理项目数据，移除不需要的字段
    const cleanedProjects = projectsData.map(project => {
      const cleanedProject = { ...project };

      // 清理项目基本信息，移除指定字段，但保留增强的启动日期
      if (cleanedProject.project) {
        const { project_id, project_path, last_updated, ...cleanedProjectInfo } = cleanedProject.project;
        // 为清理后的项目信息添加增强的启动日期格式
        cleanedProjectInfo.project_start_date = formatStartDateWithDays(cleanedProject.project.project_start_date);
        cleanedProjectInfo.project_start_date_raw = cleanedProject.project.project_start_date;
        cleanedProject.project = cleanedProjectInfo;
      }

      // 移除导出元数据中的导出时间
      if (cleanedProject.export_metadata) {
        const { export_date, ...cleanedMetadata } = cleanedProject.export_metadata;
        cleanedProject.export_metadata = cleanedMetadata;
      }

      return cleanedProject;
    });

    // 添加元数据
    const exportData = {
      metadata: {
        export_version: '2.0.0',
        total_projects: cleanedProjects.length,
        export_options: exportOptions,
        filters: {
          research_stage: selectedResearchStageId ? researchStages.find(s => s.item_id === selectedResearchStageId)?.item_value : null,
          project_status: selectedProjectStatusId ? projectStatuses.find(s => s.item_id === selectedProjectStatusId)?.item_value : null,
          disease: selectedDiseaseId ? diseases.find(d => d.item_id === selectedDiseaseId)?.item_value : null
        },
        selection: showProjectSelection ? {
          mode: 'selected_projects',
          selected_count: selectedProjectIds.length,
          total_available: projects.length
        } : {
          mode: 'filtered_or_all',
          selected_count: cleanedProjects.length,
          total_available: projects.length
        }
      },
      projects: cleanedProjects
    };

    return JSON.stringify(exportData, null, 2);
  }

  // 打开文件夹
  async function openExportFolder() {
    if (exportResult?.filePath) {
      try {
        // 获取文件所在目录
        const folderPath = exportResult.filePath.substring(0, exportResult.filePath.lastIndexOf('/'));
        await fileSystemService.openFolder(folderPath);
      } catch (error) {
        console.error('打开文件夹失败:', error);
      }
    }
  }

  // 重置状态
  function resetDialog() {
    exportResult = null;
    isExporting = false;
    selectedResearchStageId = null;
    selectedProjectStatusId = null;
    selectedDiseaseId = null;
  }

  // 关闭对话框时重置状态
  $effect(() => {
    if (!open) {
      resetDialog();
    }
  });

  // 组件挂载时加载字典数据
  onMount(() => {
    loadDictionaryData();
  });
</script>

<Dialog.Root bind:open>
  <Dialog.Content class="max-w-md">
    <Dialog.Header>
      <Dialog.Title class="text-xl">导出项目数据</Dialog.Title>
      <Dialog.Description>
        选择导出格式并保存到指定位置
      </Dialog.Description>
    </Dialog.Header>

    <div class="py-4">
      {#if !exportResult}
        <!-- 过滤器选择 -->
        <div class="mb-6">
          <h3 class="text-sm font-medium mb-3">筛选条件</h3>

          <!-- 错误提示 -->
          {#if filterError}
            <div class="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
              <p class="text-sm text-red-700">{filterError}</p>
            </div>
          {/if}

          <div class="grid grid-cols-3 gap-3 mb-4">
            <!-- 研究分期过滤器 -->
            <div>
              <label class="text-xs text-gray-600 mb-1 block">研究分期</label>
              <Select
                bind:value={selectedResearchStageId}
                placeholder="选择研究分期"
                disabled={isLoadingFilters}
                class="w-full"
                let:select
              >
                <div
                  class="px-2 py-1.5 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground cursor-pointer"
                  onclick={() => select(null, '全部分期')}
                >
                  全部分期
                </div>
                {#each researchStages as stage}
                  <div
                    class="px-2 py-1.5 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground cursor-pointer"
                    onclick={() => select(stage.item_id, stage.item_value)}
                  >
                    {stage.item_value}
                  </div>
                {/each}
              </Select>
            </div>

            <!-- 研究阶段过滤器 -->
            <div>
              <label class="text-xs text-gray-600 mb-1 block">研究阶段</label>
              <Select
                bind:value={selectedProjectStatusId}
                placeholder="选择研究阶段"
                disabled={isLoadingFilters}
                class="w-full"
                let:select
              >
                <div
                  class="px-2 py-1.5 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground cursor-pointer"
                  onclick={() => select(null, '全部阶段')}
                >
                  全部阶段
                </div>
                {#each projectStatuses as status}
                  <div
                    class="px-2 py-1.5 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground cursor-pointer"
                    onclick={() => select(status.item_id, status.item_value)}
                  >
                    {status.item_value}
                  </div>
                {/each}
              </Select>
            </div>

            <!-- 疾病过滤器 -->
            <div>
              <label class="text-xs text-gray-600 mb-1 block">疾病类型</label>
              <Select
                bind:value={selectedDiseaseId}
                placeholder="选择疾病类型"
                disabled={isLoadingFilters}
                class="w-full"
                let:select
              >
                <div
                  class="px-2 py-1.5 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground cursor-pointer"
                  onclick={() => select(null, '全部疾病')}
                >
                  全部疾病
                </div>
                {#each diseases as disease}
                  <div
                    class="px-2 py-1.5 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground cursor-pointer"
                    onclick={() => select(disease.item_id, disease.item_value)}
                  >
                    {disease.item_value}
                  </div>
                {/each}
              </Select>
            </div>
          </div>

          <!-- 过滤器状态提示和预览 -->
          {#if selectedResearchStageId || selectedProjectStatusId || selectedDiseaseId}
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-xs text-blue-700 font-medium mb-1">已应用筛选条件：</p>
                  <p class="text-xs text-blue-600">
                    {#if selectedResearchStageId}
                      研究分期 = {researchStages.find(s => s.item_id === selectedResearchStageId)?.item_value}
                    {/if}
                    {#if selectedProjectStatusId}
                      {selectedResearchStageId ? '，' : ''}研究阶段 = {projectStatuses.find(s => s.item_id === selectedProjectStatusId)?.item_value}
                    {/if}
                    {#if selectedDiseaseId}
                      {(selectedResearchStageId || selectedProjectStatusId) ? '，' : ''}疾病类型 = {diseases.find(d => d.item_id === selectedDiseaseId)?.item_value}
                    {/if}
                  </p>
                </div>
                <div class="text-right">
                  {#if isLoadingPreview}
                    <div class="flex items-center gap-1">
                      <Loader2 class="h-3 w-3 animate-spin text-blue-500" />
                      <span class="text-xs text-blue-500">计算中...</span>
                    </div>
                  {:else if filteredProjectsCount !== null}
                    <p class="text-xs text-blue-700 font-medium">
                      将导出 {filteredProjectsCount} 个项目
                    </p>
                  {/if}
                </div>
              </div>
            </div>
          {:else if filteredProjectsCount !== null}
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4">
              <p class="text-xs text-gray-600">
                将导出全部 {filteredProjectsCount} 个项目
              </p>
            </div>
          {/if}
        </div>

        <!-- 项目选择模式 -->
        <div class="mb-6">
          <h3 class="text-sm font-semibold text-gray-800 mb-3">选择模式</h3>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <label class="flex items-center gap-3 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors {!showProjectSelection ? 'border-blue-500 bg-blue-50' : ''}">
              <input
                type="radio"
                bind:group={showProjectSelection}
                value={false}
                class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 focus:ring-2"
              />
              <div class="flex-1">
                <span class="text-sm font-medium text-gray-900">按条件过滤</span>
                <p class="text-xs text-gray-600 mt-0.5">使用研究分期、项目状态等条件筛选</p>
              </div>
            </label>
            <label class="flex items-center gap-3 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors {showProjectSelection ? 'border-blue-500 bg-blue-50' : ''}">
              <input
                type="radio"
                bind:group={showProjectSelection}
                value={true}
                class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 focus:ring-2"
              />
              <div class="flex-1">
                <span class="text-sm font-medium text-gray-900">手动选择项目</span>
                <p class="text-xs text-gray-600 mt-0.5">从项目列表中逐个选择要导出的项目</p>
              </div>
            </label>
          </div>
        </div>

        <!-- 项目选择列表 -->
        {#if showProjectSelection}
          <div class="mb-6">
            <!-- 标题和全选控制 -->
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-sm font-semibold text-gray-800">选择要导出的项目</h4>
              <label class="flex items-center gap-2 cursor-pointer hover:bg-gray-50 px-2 py-1 rounded transition-colors">
                <input
                  type="checkbox"
                  bind:checked={selectAllProjects}
                  onchange={toggleSelectAll}
                  class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                />
                <span class="text-sm text-gray-700 font-medium">全选 ({projects.length})</span>
              </label>
            </div>

            <!-- 项目列表容器 -->
            <div class="border border-gray-200 rounded-lg bg-white shadow-sm">
              <div class="max-h-64 sm:max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {#each projects as project}
                  <label class="flex items-start gap-3 p-4 hover:bg-blue-50 border-b border-gray-100 last:border-b-0 cursor-pointer transition-colors group">
                    <!-- 复选框 -->
                    <input
                      type="checkbox"
                      checked={selectedProjectIds.includes(project.project.project_id || '')}
                      onchange={() => toggleProjectSelection(project.project.project_id || '')}
                      class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2 mt-0.5 flex-shrink-0"
                    />

                    <!-- 项目信息 -->
                    <div class="flex-1 min-w-0 space-y-1">
                      <!-- 项目全称 -->
                      <p class="text-sm font-medium text-gray-900 leading-5 group-hover:text-blue-900 transition-colors">
                        {project.project.project_name}
                      </p>

                      <!-- 项目简称和疾病信息 -->
                      <div class="flex flex-wrap items-center gap-2 text-xs text-gray-600">
                        <span class="bg-gray-100 px-2 py-0.5 rounded font-medium whitespace-nowrap">
                          {project.project.project_short_name}
                        </span>
                        <span class="text-gray-400 hidden sm:inline">•</span>
                        <span class="bg-blue-100 text-blue-700 px-2 py-0.5 rounded whitespace-nowrap">
                          {project.disease?.item_value || '未知疾病'}
                        </span>
                        {#if project.project_stage?.item_value}
                          <span class="text-gray-400 hidden sm:inline">•</span>
                          <span class="text-gray-600 whitespace-nowrap">
                            {project.project_stage.item_value}
                          </span>
                        {/if}
                      </div>

                      <!-- 项目状态信息 -->
                      <div class="flex flex-wrap items-center gap-2 text-xs">
                        {#if project.project_status?.item_value}
                          <span class="bg-green-100 text-green-700 px-2 py-0.5 rounded whitespace-nowrap">
                            {project.project_status.item_value}
                          </span>
                        {/if}
                        {#if project.recruitment_status?.item_value}
                          <span class="bg-orange-100 text-orange-700 px-2 py-0.5 rounded whitespace-nowrap">
                            {project.recruitment_status.item_value}
                          </span>
                        {/if}
                      </div>
                    </div>

                    <!-- 选中状态指示器 -->
                    {#if selectedProjectIds.includes(project.project.project_id || '')}
                      <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    {/if}
                  </label>
                {/each}

                <!-- 空状态 -->
                {#if projects.length === 0}
                  <div class="p-8 text-center text-gray-500">
                    <p class="text-sm">暂无可选择的项目</p>
                  </div>
                {/if}
              </div>
            </div>

            <!-- 选择状态显示 -->
            <div class="mt-3 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
              <div class="text-sm text-gray-600">
                共 {projects.length} 个项目
              </div>
              {#if selectedProjectIds.length > 0}
                <div class="flex items-center gap-2">
                  <div class="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                  <span class="text-sm font-medium text-blue-700">
                    已选择 {selectedProjectIds.length} 个项目
                  </span>
                </div>
              {:else}
                <span class="text-sm text-gray-500">
                  未选择任何项目
                </span>
              {/if}
            </div>
          </div>
        {/if}

        <!-- 导出选项 -->
        <div class="mb-4">
          <h3 class="text-sm font-medium mb-3">导出内容</h3>
          <div class="grid grid-cols-2 gap-3">
            <label class="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                bind:checked={exportOptions.includeCriteria}
                class="text-blue-600"
              />
              <span class="text-sm">入排标准</span>
            </label>
            <label class="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                bind:checked={exportOptions.includePersonnel}
                class="text-blue-600"
              />
              <span class="text-sm">人员信息</span>
            </label>
            <label class="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                bind:checked={exportOptions.includeSubsidies}
                class="text-blue-600"
              />
              <span class="text-sm">补贴信息</span>
            </label>
            <label class="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                bind:checked={exportOptions.includeDrugs}
                class="text-blue-600"
              />
              <span class="text-sm">药物信息</span>
            </label>
            <label class="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                bind:checked={exportOptions.includeSponsors}
                class="text-blue-600"
              />
              <span class="text-sm">申办方信息</span>
            </label>
          </div>
        </div>

        <!-- 格式选择 -->
        <div class="mb-4">
          <h3 class="text-sm font-medium mb-3">导出格式</h3>
        </div>
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div
            role="button"
            tabindex="0"
            class="border rounded-lg p-4 flex flex-col items-center gap-2 cursor-pointer hover:bg-gray-50 transition-colors {exportFormat === 'csv' ? 'border-blue-500 bg-blue-50' : ''}"
            onclick={() => exportFormat = 'csv'}
            onkeydown={(e) => e.key === 'Enter' && (exportFormat = 'csv')}
            aria-label="选择CSV格式"
          >
            <FileText class="h-12 w-12 text-blue-500" />
            <h3 class="font-medium">CSV 格式</h3>
            <p class="text-sm text-gray-500 text-center">导出为逗号分隔值文件，可在Excel等电子表格软件中打开</p>
          </div>

          <div
            role="button"
            tabindex="0"
            class="border rounded-lg p-4 flex flex-col items-center gap-2 cursor-pointer hover:bg-gray-50 transition-colors {exportFormat === 'json' ? 'border-blue-500 bg-blue-50' : ''}"
            onclick={() => exportFormat = 'json'}
            onkeydown={(e) => e.key === 'Enter' && (exportFormat = 'json')}
            aria-label="选择JSON格式"
          >
            <FileJson class="h-12 w-12 text-green-500" />
            <h3 class="font-medium">JSON 格式</h3>
            <p class="text-sm text-gray-500 text-center">导出为结构化JSON数据，包含完整的项目信息</p>
          </div>
        </div>

        <!-- 导出说明 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <p class="text-sm text-blue-700">
            {#if showProjectSelection && selectedProjectIds.length > 0}
              将导出您选择的 {selectedProjectIds.length} 个项目的完整数据，包括{exportOptions.includeCriteria ? '入排标准、' : ''}{exportOptions.includePersonnel ? '人员信息、' : ''}{exportOptions.includeSubsidies ? '补贴信息、' : ''}{exportOptions.includeDrugs ? '药物信息、' : ''}{exportOptions.includeSponsors ? '申办方信息、' : ''}等详细内容。
            {:else if selectedResearchStageId || selectedProjectStatusId || selectedDiseaseId}
              将根据筛选条件获取项目数据，然后导出完整的项目信息。
            {:else}
              将导出所有项目的完整数据，包括基本信息和所有关联数据。
            {/if}
            点击"导出数据"后，文件将保存到下载文件夹。
          </p>
        </div>
      {:else}
        <!-- 导出结果 -->
        <div class="text-center py-4">
          {#if exportResult.success}
            <div class="flex flex-col items-center gap-3">
              <CheckCircle class="h-12 w-12 text-green-500" />
              <h3 class="font-medium text-green-700">导出成功</h3>
              <p class="text-sm text-gray-600 break-all">{exportResult.message}</p>
              {#if exportResult.filePath}
                <Button variant="outline" onclick={openExportFolder} class="gap-2">
                  <FolderOpen class="h-4 w-4" />
                  打开文件夹
                </Button>
              {/if}
            </div>
          {:else}
            <div class="flex flex-col items-center gap-3">
              <AlertCircle class="h-12 w-12 text-red-500" />
              <h3 class="font-medium text-red-700">导出失败</h3>
              <p class="text-sm text-gray-600">{exportResult.message}</p>
            </div>
          {/if}
        </div>
      {/if}
    </div>

    <Dialog.Footer>
      <div class="flex justify-between w-full">
        <Button variant="outline" onclick={() => open = false}>
          {exportResult ? '关闭' : '取消'}
        </Button>
        {#if !exportResult}
          <div class="flex gap-2">
            <Button onclick={debugProjectData} variant="outline" size="sm">
              调试数据
            </Button>
            <Button onclick={executeExport} disabled={isExporting} class="gap-2">
              {#if isExporting}
                <Loader2 class="h-4 w-4 animate-spin" />
                导出中...
              {:else}
                <FileDown class="h-4 w-4" />
                导出数据
              {/if}
            </Button>
          </div>
        {:else if !exportResult.success}
          <Button onclick={resetDialog} variant="outline">
            重试
          </Button>
        {/if}
      </div>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
